// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  memberId?: string;
  branchId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  FIELD_OFFICER = 'field_officer',
  MEMBER = 'member'
}

// Auth Types
export interface LoginCredentials {
  identifier: string; // Can be email or member ID
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Common Types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Dashboard Types
export interface DashboardMetric {
  label: string;
  value: number | string;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: string;
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
}

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  href?: string;
  onClick?: () => void;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  disabled?: boolean;
}

export interface ActivityItem {
  id: string;
  type: 'loan_application' | 'installment_collection' | 'member_registration' | 'loan_approval' | 'transaction';
  title: string;
  description: string;
  timestamp: string;
  user?: string;
  amount?: number;
  status?: 'pending' | 'completed' | 'failed';
}

export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface PerformanceData {
  period: string;
  collections: number;
  disbursements: number;
  members: number;
}

// Field Officer Dashboard Types
export interface FieldOfficerMetrics {
  totalMembers: number;
  totalCollections: number;
  monthlyTarget: number;
  achievementPercentage: number;
  pendingInstallments: number;
  overdueInstallments: number;
}

export interface FieldOfficerDashboardData {
  metrics: FieldOfficerMetrics;
  recentActivities: ActivityItem[];
  quickActions: QuickAction[];
  upcomingCollections: InstallmentSummary[];
}

// Branch Manager Dashboard Types
export interface BranchMetrics {
  totalFieldOfficers: number;
  totalActiveLoans: number;
  totalMembers: number;
  monthlyCollectionTarget: number;
  collectedAmount: number;
  pendingInstallments: number;
  overdueInstallments: number;
  branchIncome: number;
  branchExpenses: number;
}

export interface BranchManagerDashboardData {
  metrics: BranchMetrics;
  installmentStatus: {
    pending: number;
    overdue: number;
    collected: number;
  };
  fieldOfficerPerformance: FieldOfficerPerformance[];
  recentActivities: ActivityItem[];
  quickActions: QuickAction[];
}

export interface FieldOfficerPerformance {
  id: string;
  name: string;
  membersCount: number;
  collectionsThisMonth: number;
  targetAchievement: number;
}

// Admin Dashboard Types
export interface SystemMetrics {
  totalBranches: number;
  totalUsers: number;
  totalFieldOfficers: number;
  totalMembers: number;
  totalActiveLoans: number;
  totalLoanAmount: number;
  totalCollections: number;
  systemUptime: string;
}

export interface AdminDashboardData {
  metrics: SystemMetrics;
  financialPerformance: PerformanceData[];
  branchPerformance: BranchPerformance[];
  recentActivities: ActivityItem[];
  quickActions: QuickAction[];
  alerts: SystemAlert[];
}

export interface BranchPerformance {
  id: string;
  name: string;
  managerId: string;
  managerName: string;
  totalMembers: number;
  activeLoans: number;
  collectionsThisMonth: number;
  performance: number;
}

export interface SystemAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
}

// Member Dashboard Types
export interface MemberFinancialSummary {
  savingsBalance: number;
  totalSavings: number;
  activeLoanAmount: number;
  totalLoanAmount: number;
  nextPaymentAmount: number;
  nextPaymentDate: string;
  creditScore: number;
}

export interface LoanDetails {
  id: string;
  loanAmount: number;
  remainingAmount: number;
  installmentAmount: number;
  nextInstallmentDate: string;
  totalInstallments: number;
  paidInstallments: number;
  status: 'active' | 'completed' | 'overdue';
}

export interface PaymentHistory {
  id: string;
  date: string;
  amount: number;
  type: 'installment' | 'savings' | 'fee';
  status: 'completed' | 'pending' | 'failed';
  description: string;
}

export interface MemberDashboardData {
  financialSummary: MemberFinancialSummary;
  activeLoans: LoanDetails[];
  recentPayments: PaymentHistory[];
  savingsAccounts: SavingAccountSummary[];
  quickActions: QuickAction[];
}

export interface SavingAccountSummary {
  id: string;
  type: 'general' | 'dps' | 'fdr';
  balance: number;
  monthlyAmount?: number;
  maturityDate?: string;
  interestRate: number;
}

// Installment Types
export interface InstallmentSummary {
  id: string;
  memberName: string;
  memberId: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'overdue' | 'paid';
  loanId: string;
  installmentNo: number;
}
